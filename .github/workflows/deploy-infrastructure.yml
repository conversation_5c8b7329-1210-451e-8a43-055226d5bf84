name: Deploy Data Pipeline Infrastructure

on:
  push:
    branches:
      - main
      - develop
    paths:
      - 'infrastructure/**'
      - '.github/workflows/deploy-infrastructure.yml'
  pull_request:
    branches:
      - main
    paths:
      - 'infrastructure/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      action:
        description: 'Terraform action to perform'
        required: true
        default: 'apply'
        type: choice
        options:
          - plan
          - apply
          - destroy

env:
  TF_VERSION: '1.6.0'
  TF_WORKING_DIR: './infrastructure/terraform'

jobs:
  terraform-plan:
    name: Terraform Plan
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    
    outputs:
      tfplanExitCode: ${{ steps.tf-plan.outputs.exitcode }}
      
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Create Terraform Backend Bucket
        run: |
          BUCKET_NAME="${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}"
          if ! gsutil ls -b gs://$BUCKET_NAME 2>/dev/null; then
            echo "Creating Terraform state bucket: $BUCKET_NAME"
            gsutil mb -p ${{ secrets.GCP_PROJECT_ID }} -l ${{ secrets.GCP_REGION }} gs://$BUCKET_NAME
            gsutil versioning set on gs://$BUCKET_NAME
          else
            echo "Terraform state bucket already exists: $BUCKET_NAME"
          fi

      - name: Create terraform.tfvars
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          cat > terraform.tfvars << EOF
          project_id = "${{ secrets.GCP_PROJECT_ID }}"
          project_name = "data-pipeline-${{ github.event.inputs.environment || 'dev' }}"
          region = "${{ secrets.GCP_REGION }}"
          zone = "${{ secrets.GCP_ZONE }}"
          machine_type = "${{ secrets.VM_MACHINE_TYPE || 'e2-standard-4' }}"
          aws_private_key = "${{ secrets.AWS_PRIVATE_KEY }}"
          aws_public_key = "${{ secrets.AWS_PUBLIC_KEY }}"
          aws_hostname = "${{ secrets.AWS_HOSTNAME }}"
          aws_user = "${{ secrets.AWS_USER }}"
          github_repo = "${{ github.server_url }}/${{ github.repository }}"
          github_token = "${{ secrets.GITHUB_TOKEN }}"
          environment = "${{ github.event.inputs.environment || 'dev' }}"
          EOF

      - name: Update Terraform Backend Configuration
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          BUCKET_NAME="${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}"
          sed -i "s/your-terraform-state-bucket/$BUCKET_NAME/g" main.tf

      - name: Terraform Init
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          terraform init \
            -backend-config="bucket=${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}" \
            -backend-config="prefix=terraform/state/${{ github.event.inputs.environment || 'dev' }}"

      - name: Terraform Format Check
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: terraform fmt -check

      - name: Terraform Validate
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: terraform validate

      - name: Terraform Plan
        id: tf-plan
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          export exitcode=0
          terraform plan -detailed-exitcode -no-color -out tfplan || export exitcode=$?
          
          echo "exitcode=$exitcode" >> $GITHUB_OUTPUT
          
          if [ $exitcode -eq 1 ]; then
            echo Terraform Plan Failed!
            exit 1
          else 
            exit 0
          fi

      - name: Publish Terraform Plan
        uses: actions/upload-artifact@v4
        with:
          name: tfplan-${{ github.event.inputs.environment || 'dev' }}
          path: ${{ env.TF_WORKING_DIR }}/tfplan

      - name: Create Plan Summary
        uses: actions/github-script@v7
        if: github.event_name == 'pull_request'
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const fs = require('fs');
            const { execSync } = require('child_process');
            
            // Get terraform show output
            const planOutput = execSync('cd ${{ env.TF_WORKING_DIR }} && terraform show -no-color tfplan', { encoding: 'utf-8' });
            
            const output = `#### Terraform Plan 📖 \`${{ steps.tf-plan.outcome }}\`
            
            <details><summary>Show Plan</summary>
            
            \`\`\`terraform
            ${planOutput}
            \`\`\`
            
            </details>
            
            *Pusher: @${{ github.actor }}, Action: \`${{ github.event_name }}\`, Environment: \`${{ github.event.inputs.environment || 'dev' }}\`*`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: output
            });

  terraform-apply:
    name: Terraform Apply
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    needs: [terraform-plan]
    if: |
      (github.ref == 'refs/heads/main' && needs.terraform-plan.outputs.tfplanExitCode == 2) ||
      (github.event.inputs.action == 'apply') ||
      (github.event.inputs.action == 'destroy')
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Download Terraform Plan
        uses: actions/download-artifact@v4
        if: github.event.inputs.action != 'destroy'
        with:
          name: tfplan-${{ github.event.inputs.environment || 'dev' }}
          path: ${{ env.TF_WORKING_DIR }}

      - name: Create terraform.tfvars
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          cat > terraform.tfvars << EOF
          project_id = "${{ secrets.GCP_PROJECT_ID }}"
          project_name = "data-pipeline-${{ github.event.inputs.environment || 'dev' }}"
          region = "${{ secrets.GCP_REGION }}"
          zone = "${{ secrets.GCP_ZONE }}"
          machine_type = "${{ secrets.VM_MACHINE_TYPE || 'e2-standard-4' }}"
          aws_private_key = "${{ secrets.AWS_PRIVATE_KEY }}"
          aws_public_key = "${{ secrets.AWS_PUBLIC_KEY }}"
          aws_hostname = "${{ secrets.AWS_HOSTNAME }}"
          aws_user = "${{ secrets.AWS_USER }}"
          github_repo = "${{ github.server_url }}/${{ github.repository }}"
          github_token = "${{ secrets.GITHUB_TOKEN }}"
          environment = "${{ github.event.inputs.environment || 'dev' }}"
          EOF

      - name: Update Terraform Backend Configuration
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          BUCKET_NAME="${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}"
          sed -i "s/your-terraform-state-bucket/$BUCKET_NAME/g" main.tf

      - name: Terraform Init
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          terraform init \
            -backend-config="bucket=${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}" \
            -backend-config="prefix=terraform/state/${{ github.event.inputs.environment || 'dev' }}"

      - name: Terraform Apply
        if: github.event.inputs.action != 'destroy'
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: terraform apply -auto-approve tfplan

      - name: Terraform Destroy
        if: github.event.inputs.action == 'destroy'
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: terraform destroy -auto-approve

      - name: Get VM Information
        if: github.event.inputs.action != 'destroy'
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          echo "=== Terraform Outputs ==="
          terraform output
          
          echo "=== VM Information ==="
          VM_NAME=$(terraform output -raw vm_name)
          VM_ZONE=$(terraform output -raw vm_zone)
          PROJECT_ID="${{ secrets.GCP_PROJECT_ID }}"
          
          echo "VM Name: $VM_NAME"
          echo "VM Zone: $VM_ZONE"
          echo "Project ID: $PROJECT_ID"
          
          echo "=== VM Startup Logs ==="
          gcloud compute instances get-serial-port-output $VM_NAME --zone=$VM_ZONE --project=$PROJECT_ID || echo "Could not retrieve startup logs"
