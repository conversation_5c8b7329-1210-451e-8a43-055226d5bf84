name: Run Data Pipeline

on:
  schedule:
    - cron: '0 2 * * 0'  # Weekly on Sunday at 2 AM UTC
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to run pipeline in'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      skip_vm_creation:
        description: 'Skip VM creation (use existing VM)'
        required: false
        default: false
        type: boolean

env:
  TF_VERSION: '1.6.0'
  TF_WORKING_DIR: './infrastructure/terraform'

jobs:
  create-vm:
    name: Create Pipeline VM
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'prod' }}
    if: ${{ !github.event.inputs.skip_vm_creation }}
    
    outputs:
      vm_name: ${{ steps.get-vm-info.outputs.vm_name }}
      vm_zone: ${{ steps.get-vm-info.outputs.vm_zone }}
      
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}
          terraform_wrapper: false

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Create terraform.tfvars
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          cat > terraform.tfvars << EOF
          project_id = "${{ secrets.GCP_PROJECT_ID }}"
          project_name = "data-pipeline-${{ github.event.inputs.environment || 'prod' }}"
          region = "${{ secrets.GCP_REGION }}"
          zone = "${{ secrets.GCP_ZONE }}"
          machine_type = "${{ secrets.VM_MACHINE_TYPE || 'e2-standard-4' }}"
          aws_private_key = "${{ secrets.AWS_PRIVATE_KEY }}"
          aws_public_key = "${{ secrets.AWS_PUBLIC_KEY }}"
          aws_hostname = "${{ secrets.AWS_HOSTNAME }}"
          aws_user = "${{ secrets.AWS_USER }}"
          github_repo = "${{ github.server_url }}/${{ github.repository }}"
          github_token = "${{ secrets.GITHUB_TOKEN }}"
          environment = "${{ github.event.inputs.environment || 'prod' }}"
          auto_delete_vm = true
          EOF

      - name: Update Terraform Backend Configuration
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          BUCKET_NAME="${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'prod' }}"
          sed -i "s/your-terraform-state-bucket/$BUCKET_NAME/g" main.tf

      - name: Terraform Init
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          terraform init \
            -backend-config="bucket=${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'prod' }}" \
            -backend-config="prefix=terraform/state/${{ github.event.inputs.environment || 'prod' }}"

      - name: Terraform Apply
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: terraform apply -auto-approve

      - name: Get VM Information
        id: get-vm-info
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          VM_NAME=$(terraform output -raw vm_name)
          VM_ZONE=$(terraform output -raw vm_zone)
          
          echo "vm_name=$VM_NAME" >> $GITHUB_OUTPUT
          echo "vm_zone=$VM_ZONE" >> $GITHUB_OUTPUT
          
          echo "VM Name: $VM_NAME"
          echo "VM Zone: $VM_ZONE"

      - name: Wait for VM Startup
        run: |
          VM_NAME="${{ steps.get-vm-info.outputs.vm_name }}"
          VM_ZONE="${{ steps.get-vm-info.outputs.vm_zone }}"
          PROJECT_ID="${{ secrets.GCP_PROJECT_ID }}"
          
          echo "Waiting for VM startup to complete..."
          
          # Wait up to 10 minutes for startup script to complete
          for i in {1..60}; do
            echo "Checking startup progress... (attempt $i/60)"
            
            # Check if startup script completed successfully
            if gcloud compute instances get-serial-port-output $VM_NAME --zone=$VM_ZONE --project=$PROJECT_ID 2>/dev/null | grep -q "Data Pipeline VM Startup Script Completed Successfully"; then
              echo "VM startup completed successfully!"
              break
            fi
            
            if [ $i -eq 60 ]; then
              echo "VM startup timeout after 10 minutes"
              exit 1
            fi
            
            sleep 10
          done

      - name: Display VM Startup Logs
        run: |
          VM_NAME="${{ steps.get-vm-info.outputs.vm_name }}"
          VM_ZONE="${{ steps.get-vm-info.outputs.vm_zone }}"
          PROJECT_ID="${{ secrets.GCP_PROJECT_ID }}"
          
          echo "=== VM Startup Logs ==="
          gcloud compute instances get-serial-port-output $VM_NAME --zone=$VM_ZONE --project=$PROJECT_ID || echo "Could not retrieve startup logs"

  run-pipeline:
    name: Execute Data Pipeline
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'prod' }}
    needs: [create-vm]
    if: always() && (needs.create-vm.result == 'success' || github.event.inputs.skip_vm_creation)
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Get VM Information
        id: vm-info
        run: |
          if [ "${{ github.event.inputs.skip_vm_creation }}" == "true" ]; then
            # Use existing VM
            VM_NAME="data-pipeline-${{ github.event.inputs.environment || 'prod' }}-pipeline-vm"
            VM_ZONE="${{ secrets.GCP_ZONE }}"
          else
            # Use newly created VM
            VM_NAME="${{ needs.create-vm.outputs.vm_name }}"
            VM_ZONE="${{ needs.create-vm.outputs.vm_zone }}"
          fi
          
          echo "vm_name=$VM_NAME" >> $GITHUB_OUTPUT
          echo "vm_zone=$VM_ZONE" >> $GITHUB_OUTPUT

      - name: Execute Pipeline on VM
        run: |
          VM_NAME="${{ steps.vm-info.outputs.vm_name }}"
          VM_ZONE="${{ steps.vm-info.outputs.vm_zone }}"
          PROJECT_ID="${{ secrets.GCP_PROJECT_ID }}"
          
          echo "Executing data pipeline on VM: $VM_NAME"
          
          # Execute pipeline commands on the VM
          gcloud compute ssh $VM_NAME \
            --zone=$VM_ZONE \
            --project=$PROJECT_ID \
            --command="
              set -e
              echo 'Starting data pipeline execution...'
              
              # Switch to pipeline user
              sudo -u pipeline bash << 'PIPELINE_EOF'
              cd /home/<USER>
              
              # Test SSH connection again
              echo 'Testing SSH connection to AWS EC2...'
              ssh trips 'echo \"SSH connection verified: \$(date)\"' || {
                echo 'SSH connection failed, attempting to restart ssh-agent...'
                eval \"\$(ssh-agent -s)\"
                ssh-add ~/.ssh/aws_private_key
                ssh trips 'echo \"SSH connection verified after restart: \$(date)\"'
              }
              
              # If pipeline repository exists, run the pipeline
              if [ -d '/home/<USER>/pipeline-repo' ]; then
                cd /home/<USER>/pipeline-repo
                
                # Activate virtual environment if it exists
                if [ -f 'venv/bin/activate' ]; then
                  source venv/bin/activate
                fi
                
                # Run pipeline script if it exists
                if [ -f 'run_pipeline.py' ]; then
                  echo 'Running Python pipeline...'
                  python run_pipeline.py
                elif [ -f 'run_pipeline.sh' ]; then
                  echo 'Running shell pipeline...'
                  bash run_pipeline.sh
                else
                  echo 'No pipeline script found, running basic data extraction test...'
                  
                  # Basic test: dump a sample table from AWS EC2
                  ssh trips 'mysqldump --single-transaction --routines --triggers --all-databases' > /tmp/aws_db_dump.sql
                  
                  # Import to local MariaDB
                  mysql -u pipeline -ppipeline123 pipeline_data < /tmp/aws_db_dump.sql
                  
                  # Basic verification
                  mysql -u pipeline -ppipeline123 -e \"SHOW DATABASES;\"
                  
                  echo 'Basic pipeline test completed successfully'
                fi
              else
                echo 'Pipeline repository not found, running basic SSH test only'
              fi
              
              echo 'Pipeline execution completed at: \$(date)'
PIPELINE_EOF
            " || echo "Pipeline execution failed"

      - name: Collect Pipeline Logs
        run: |
          VM_NAME="${{ steps.vm-info.outputs.vm_name }}"
          VM_ZONE="${{ steps.vm-info.outputs.vm_zone }}"
          PROJECT_ID="${{ secrets.GCP_PROJECT_ID }}"
          
          echo "=== Collecting Pipeline Execution Logs ==="
          
          # Get the latest logs from the VM
          gcloud compute ssh $VM_NAME \
            --zone=$VM_ZONE \
            --project=$PROJECT_ID \
            --command="
              echo '=== Pipeline Startup Log ==='
              sudo cat /var/log/pipeline-startup.log 2>/dev/null || echo 'Startup log not found'
              
              echo '=== SSH Test Results ==='
              sudo -u pipeline cat /home/<USER>/ssh_test_output.log 2>/dev/null || echo 'SSH test log not found'
              
              echo '=== VM Setup Summary ==='
              sudo -u pipeline cat /home/<USER>/vm_setup_summary.txt 2>/dev/null || echo 'Setup summary not found'
            " || echo "Could not collect logs from VM"

  cleanup-vm:
    name: Cleanup Pipeline VM
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'prod' }}
    needs: [create-vm, run-pipeline]
    if: always() && needs.create-vm.result == 'success' && !github.event.inputs.skip_vm_creation
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}
          terraform_wrapper: false

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Destroy Infrastructure
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          # Create terraform.tfvars for destroy
          cat > terraform.tfvars << EOF
          project_id = "${{ secrets.GCP_PROJECT_ID }}"
          project_name = "data-pipeline-${{ github.event.inputs.environment || 'prod' }}"
          region = "${{ secrets.GCP_REGION }}"
          zone = "${{ secrets.GCP_ZONE }}"
          machine_type = "${{ secrets.VM_MACHINE_TYPE || 'e2-standard-4' }}"
          aws_private_key = "${{ secrets.AWS_PRIVATE_KEY }}"
          aws_public_key = "${{ secrets.AWS_PUBLIC_KEY }}"
          aws_hostname = "${{ secrets.AWS_HOSTNAME }}"
          aws_user = "${{ secrets.AWS_USER }}"
          github_repo = "${{ github.server_url }}/${{ github.repository }}"
          github_token = "${{ secrets.GITHUB_TOKEN }}"
          environment = "${{ github.event.inputs.environment || 'prod' }}"
          EOF
          
          # Update backend configuration
          BUCKET_NAME="${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'prod' }}"
          sed -i "s/your-terraform-state-bucket/$BUCKET_NAME/g" main.tf
          
          # Initialize and destroy
          terraform init \
            -backend-config="bucket=${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'prod' }}" \
            -backend-config="prefix=terraform/state/${{ github.event.inputs.environment || 'prod' }}"
          
          terraform destroy -auto-approve
