terraform {
  required_version = ">= 1.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
  backend "gcs" {
    bucket = "your-terraform-state-bucket"
    prefix = "terraform/state"
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

locals {
  vm_name = "${var.project_name}-pipeline-vm"
  
  startup_script = templatefile("${path.module}/startup-script.sh", {
    private_key_content = var.aws_private_key
    public_key_content  = var.aws_public_key
    aws_hostname       = var.aws_hostname
    aws_user          = var.aws_user
    github_repo       = var.github_repo
    github_token      = var.github_token
  })
}

resource "google_compute_network" "pipeline_network" {
  name                    = "${var.project_name}-network"
  auto_create_subnetworks = false
  description            = "Network for data pipeline VM"
}

resource "google_compute_subnetwork" "pipeline_subnet" {
  name          = "${var.project_name}-subnet"
  ip_cidr_range = "********/24"
  region        = var.region
  network       = google_compute_network.pipeline_network.id
  description   = "Subnet for data pipeline VM"
}

resource "google_compute_firewall" "allow_ssh" {
  name    = "${var.project_name}-allow-ssh"
  network = google_compute_network.pipeline_network.name

  allow {
    protocol = "tcp"
    ports    = ["22"]
  }

  source_ranges = ["0.0.0.0/0"]
  target_tags   = ["pipeline-vm"]
  description   = "Allow SSH access to pipeline VM"
}

resource "google_compute_firewall" "allow_internal" {
  name    = "${var.project_name}-allow-internal"
  network = google_compute_network.pipeline_network.name

  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }

  allow {
    protocol = "udp"
    ports    = ["0-65535"]
  }

  allow {
    protocol = "icmp"
  }

  source_ranges = ["********/24"]
  target_tags   = ["pipeline-vm"]
  description   = "Allow internal communication"
}

# Use existing service account
data "google_service_account" "pipeline_vm_sa" {
  account_id = "vm-cuba-buddy-data-ingestion"
  project    = var.project_id
}

# Ensure the existing service account has required permissions
resource "google_project_iam_member" "pipeline_vm_storage_admin" {
  project = var.project_id
  role    = "roles/storage.admin"
  member  = "serviceAccount:${data.google_service_account.pipeline_vm_sa.email}"
}

resource "google_project_iam_member" "pipeline_vm_compute_admin" {
  project = var.project_id
  role    = "roles/compute.instanceAdmin"
  member  = "serviceAccount:${data.google_service_account.pipeline_vm_sa.email}"
}

resource "google_project_iam_member" "pipeline_vm_logging_writer" {
  project = var.project_id
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${data.google_service_account.pipeline_vm_sa.email}"
}

resource "google_project_iam_member" "pipeline_vm_monitoring_writer" {
  project = var.project_id
  role    = "roles/monitoring.metricWriter"
  member  = "serviceAccount:${data.google_service_account.pipeline_vm_sa.email}"
}

resource "google_compute_instance" "pipeline_vm" {
  name         = local.vm_name
  machine_type = var.machine_type
  zone         = var.zone
  tags         = ["pipeline-vm"]

  boot_disk {
    initialize_params {
      image = var.vm_image
      size  = var.disk_size
      type  = "pd-standard"
    }
  }

  network_interface {
    network    = google_compute_network.pipeline_network.id
    subnetwork = google_compute_subnetwork.pipeline_subnet.id
    
    access_config {
      # Ephemeral public IP
    }
  }

  service_account {
    email  = data.google_service_account.pipeline_vm_sa.email
    scopes = [
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/monitoring.write"
    ]
  }

  metadata = {
    enable-oslogin = "FALSE"
    startup-script = local.startup_script
  }

  metadata_startup_script = local.startup_script

  lifecycle {
    create_before_destroy = true
  }

  depends_on = [
    google_compute_network.pipeline_network,
    google_compute_subnetwork.pipeline_subnet,
    data.google_service_account.pipeline_vm_sa
  ]
}

resource "google_storage_bucket" "pipeline_data" {
  name          = "${var.project_id}-pipeline-data-${random_id.bucket_suffix.hex}"
  location      = var.region
  force_destroy = true

  uniform_bucket_level_access = true

  versioning {
    enabled = true
  }

  lifecycle_rule {
    condition {
      age = 30
    }
    action {
      type = "Delete"
    }
  }
}

resource "random_id" "bucket_suffix" {
  byte_length = 4
}

resource "google_storage_bucket_iam_member" "pipeline_vm_bucket_admin" {
  bucket = google_storage_bucket.pipeline_data.name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${data.google_service_account.pipeline_vm_sa.email}"
}
