variable "project_id" {
  description = "The GCP project ID"
  type        = string
  default     = "external-data-source-437915"
}

variable "project_name" {
  description = "Name prefix for all resources"
  type        = string
  default     = "data-pipeline"
}

variable "region" {
  description = "The GCP region"
  type        = string
  default     = "us-central1"
}

variable "zone" {
  description = "The GCP zone"
  type        = string
  default     = "us-central1-a"
}

variable "machine_type" {
  description = "The machine type for the VM"
  type        = string
  default     = "e2-standard-4"
}

variable "vm_image" {
  description = "The VM image to use"
  type        = string
  default     = "ubuntu-os-cloud/ubuntu-2204-lts"
}

variable "disk_size" {
  description = "The size of the boot disk in GB"
  type        = number
  default     = 50
}

variable "aws_private_key" {
  description = "AWS EC2 private key content"
  type        = string
  sensitive   = true
}

variable "aws_public_key" {
  description = "AWS EC2 public key content"
  type        = string
  sensitive   = true
}

variable "aws_hostname" {
  description = "AWS EC2 hostname/IP"
  type        = string
  default     = "***********"
}

variable "aws_user" {
  description = "AWS EC2 username"
  type        = string
  default     = "forge"
}

variable "github_repo" {
  description = "GitHub repository URL"
  type        = string
}

variable "github_token" {
  description = "GitHub personal access token"
  type        = string
  sensitive   = true
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "auto_delete_vm" {
  description = "Whether to automatically delete the VM after pipeline completion"
  type        = bool
  default     = true
}

variable "pipeline_schedule" {
  description = "Cron schedule for the pipeline"
  type        = string
  default     = "0 2 * * 0"  # Weekly on Sunday at 2 AM
}
