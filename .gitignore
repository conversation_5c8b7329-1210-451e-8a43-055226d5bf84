# Data Pipeline Project .gitignore

# ===== SENSITIVE FILES =====
# SSH Keys and Credentials
*.pem
*.key
*_key
*_key.pub
id_rsa*
id_ed25519*
*.ppk
ssh_config
known_hosts

# Environment and Configuration Files
.env
.env.*
*.env
config.json
secrets.json
credentials.json
service-account*.json

# Database Credentials and Dumps
*.sql
*.dump
*.backup
database.conf
db_config.*

# AWS Credentials
.aws/
aws-credentials*
*.aws

# GCP Credentials
gcp-credentials*
*.gcp
application_default_credentials.json

# ===== TERRAFORM =====
# Terraform State Files
*.tfstate
*.tfstate.*
*.tfstate.backup
.terraform/
.terraform.lock.hcl
terraform.tfvars
terraform.tfvars.json
*.auto.tfvars
*.auto.tfvars.json
crash.log
crash.*.log
override.tf
override.tf.json
*_override.tf
*_override.tf.json
.terraformrc
terraform.rc

# Terraform Plan Files
tfplan
*.tfplan

# ===== PYTHON =====
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
pipenv/
.pipenv/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# ===== NODE.JS =====
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/

# ===== LOGS AND MONITORING =====
# Log files
*.log
logs/
log/
*.log.*
*.out
*.err

# Runtime logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Application logs
application.log
error.log
access.log
debug.log
pipeline.log
startup.log

# Monitoring and metrics
metrics/
monitoring/
alerts/

# ===== DATA FILES =====
# Data dumps and exports
*.csv
*.tsv
*.json
*.xml
*.parquet
*.avro
*.orc

# Database files
*.db
*.sqlite
*.sqlite3

# Compressed files
*.zip
*.tar.gz
*.tar.bz2
*.rar
*.7z

# Large data files
data/
datasets/
exports/
dumps/
backups/

# ===== DEVELOPMENT TOOLS =====
# IDE and Editor files
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ===== DOCKER =====
# Docker files (if containing sensitive info)
docker-compose.override.yml
.dockerignore

# ===== CLOUD FUNCTIONS =====
# Cloud Functions
functions/node_modules/
functions/.env
functions/config.json

# ===== TESTING =====
# Test results
test-results/
test-output/
coverage/
.nyc_output/

# ===== MISCELLANEOUS =====
# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Lock files (except package-lock.json for reproducible builds)
yarn.lock
pnpm-lock.yaml

# Build artifacts
build/
dist/
out/

# Cache directories
.cache/
.npm/
.yarn/

# Local configuration
local.json
local.yml
local.yaml

# Backup files
*.bak
*.backup
*.old

# Archive files
*.tar
*.gz
*.zip
*.rar

# Documentation build
docs/_build/
site/

# Certificates
*.crt
*.cert
*.ca-bundle
*.p12
*.pfx

# Local development
.local/
local/
