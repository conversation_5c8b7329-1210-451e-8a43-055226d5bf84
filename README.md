# AWS-to-GCP Data Pipeline

A comprehensive data pipeline system that extracts data from AWS EC2 MariaDB instances, transforms it, and stores the processed data in Google Cloud Storage using ephemeral GCP Compute Engine VMs.

## 🏗️ Architecture

```
AWS EC2 (MariaDB) → SSH → GCP VM → Data Processing → Google Cloud Storage
                                ↓
                           Self-Destruct
```

### Key Components:
- **AWS EC2**: Source MariaDB/MySQL database
- **GCP Compute Engine**: Temporary processing VM
- **Google Cloud Storage**: Destination for processed data
- **GitHub Actions**: CI/CD and scheduling
- **Terraform**: Infrastructure as Code

## 🚀 Quick Start

### Prerequisites
- Google Cloud Platform account
- AWS EC2 instance with MariaDB/MySQL
- GitHub repository
- SSH access to AWS EC2

### 1. Setup
```bash
git clone https://github.com/your-username/gcp_tools.git
cd gcp_tools
```

### 2. Configure GitHub Secrets
Set up the following secrets in your GitHub repository:
- `GCP_SA_KEY` - Google Cloud service account key
- `GCP_PROJECT_ID` - Your GCP project ID
- `AWS_PRIVATE_KEY` - SSH private key for AWS EC2
- `AWS_PUBLIC_KEY` - SSH public key for AWS EC2
- `AWS_HOSTNAME` - AWS EC2 IP address
- `AWS_USER` - AWS EC2 username

📖 **Detailed setup guide**: [docs/setup/github-secrets-setup.md](docs/setup/github-secrets-setup.md)

### 3. Deploy Infrastructure
1. Go to **Actions** → **Deploy Data Pipeline Infrastructure**
2. Run workflow with your environment (dev/staging/prod)
3. Choose "apply" to create resources

### 4. Run Pipeline
1. Go to **Actions** → **Run Data Pipeline**
2. Select environment and run
3. Monitor logs for progress

### 5. Destroy Infrastructure (When Needed)
1. Go to **Actions** → **🗑️ Destroy Data Pipeline Infrastructure**
2. Select environment and type "DESTROY" to confirm
3. All resources will be permanently deleted

## 📁 Project Structure

```
gcp_tools/
├── infrastructure/           # Terraform infrastructure code
│   └── terraform/
│       ├── main.tf          # Main Terraform configuration
│       ├── variables.tf     # Variable definitions
│       ├── outputs.tf       # Output definitions
│       └── startup-script.sh # VM startup script
├── data-pipeline/           # Data processing code
│   ├── etl/                # Extract, Transform, Load
│   ├── transformations/    # Data transformation logic
│   └── utils/              # Utility functions
├── cloud-functions/        # Cloud Functions for orchestration
├── config/                 # Configuration files
│   └── environments/       # Environment-specific configs
├── docs/                   # Documentation
│   ├── setup/             # Setup guides
│   └── architecture/      # Architecture documentation
├── tests/                  # Test files
├── .github/workflows/      # GitHub Actions workflows
├── run_pipeline.py         # Main pipeline script
└── requirements.txt        # Python dependencies
```

## 🔄 Pipeline Process

1. **VM Creation**: Terraform creates a GCP Compute Engine VM
2. **SSH Setup**: VM configures SSH keys and tests AWS EC2 connection
3. **Data Extraction**: Connects to AWS EC2 and dumps MariaDB database
4. **Data Processing**: Imports data locally and performs transformations
5. **Upload**: Processed data uploaded to Google Cloud Storage
6. **Cleanup**: VM self-destructs after completion

## 📊 Monitoring

### View Logs
```bash
# VM startup logs
gcloud compute instances get-serial-port-output VM_NAME --zone=ZONE

# Cloud Logging
gcloud logging read "resource.type=gce_instance" --limit=50
```

### SSH to VM (for debugging)
```bash
gcloud compute ssh VM_NAME --zone=ZONE
```

## 🔧 Configuration

### Environment Variables
Configure in `config/environments/{env}/terraform.tfvars.template`:

```hcl
project_id = "your-gcp-project"
aws_hostname = "***********"
aws_user = "forge"
machine_type = "e2-standard-4"
```

### Pipeline Schedule
- **Development**: Manual trigger
- **Staging**: Daily (for testing)
- **Production**: Weekly (Sunday 2 AM UTC)

## 🧪 Testing

### Local Testing
```bash
# Install dependencies
pip install -r requirements.txt

# Run tests
pytest tests/

# Validate Terraform
cd infrastructure/terraform
terraform validate
terraform plan
```

### Integration Testing
The pipeline includes automated tests for:
- SSH connectivity
- Database operations
- GCS uploads
- Infrastructure deployment

## 🔒 Security Features

- **Ephemeral VMs**: Temporary infrastructure reduces attack surface
- **SSH Key Management**: Secure key handling and rotation
- **IAM Permissions**: Least-privilege access controls
- **Encrypted Storage**: Data encrypted in transit and at rest
- **Audit Logging**: Complete audit trail in Cloud Logging

## 📈 Scaling

### Performance Tuning
- Adjust VM machine type based on data volume
- Configure parallel processing for large datasets
- Optimize database dump and import operations

### Multi-Environment Support
- Separate configurations for dev/staging/prod
- Environment-specific resource sizing
- Isolated state management

## 🚨 Troubleshooting

### Common Issues

1. **SSH Connection Failed**
   - Check AWS security groups
   - Verify SSH key format
   - Ensure EC2 instance is running

2. **Terraform Errors**
   - Verify GCP permissions
   - Check resource quotas
   - Validate configuration syntax

3. **Pipeline Failures**
   - Review VM startup logs
   - Check database connectivity
   - Verify GCS permissions

📖 **Full troubleshooting guide**: [docs/setup/README.md](docs/setup/README.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

- **Documentation**: Check the `docs/` directory
- **Issues**: Create a GitHub issue
- **Discussions**: Use GitHub Discussions for questions

## 🔄 Roadmap

- [ ] Support for multiple database types
- [ ] Real-time data streaming
- [ ] Advanced data transformation pipelines
- [ ] Integration with Apache Airflow
- [ ] Cost optimization features
- [ ] Multi-cloud support

---

**Built with ❤️ for reliable data pipeline automation**
